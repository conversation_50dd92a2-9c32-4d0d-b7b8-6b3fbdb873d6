using BlazorAiChat.Models;
using System.Collections.Concurrent;
using System.Numerics.Tensors;

namespace BlazorAiChat.Services;

public class InMemoryVectorStore : IVectorStore
{
    private readonly ConcurrentDictionary<string, DocumentChunk> _chunks = new();
    private readonly ILogger<InMemoryVectorStore> _logger;
    private readonly object _lockObject = new();

    public InMemoryVectorStore(ILogger<InMemoryVectorStore> logger)
    {
        _logger = logger;
    }

    public async Task AddChunkAsync(DocumentChunk chunk)
    {
        await Task.Run(() =>
        {
            _chunks.AddOrUpdate(chunk.Id, chunk, (key, oldValue) => chunk);
            _logger.LogDebug("Added chunk {ChunkId} from {SourceFile}", chunk.Id, chunk.SourceFile);
        });
    }

    public async Task AddChunksAsync(IEnumerable<DocumentChunk> chunks)
    {
        await Task.Run(() =>
        {
            foreach (var chunk in chunks)
            {
                _chunks.AddOrUpdate(chunk.Id, chunk, (key, oldValue) => chunk);
            }
            _logger.LogInformation("Added {ChunkCount} chunks to vector store", chunks.Count());
        });
    }

    public async Task<List<SimilarityResult>> SearchSimilarAsync(float[] queryEmbedding, int topK = 5, float threshold = 0.0f)
    {
        return await Task.Run(() =>
        {
            var results = new List<SimilarityResult>();

            if (queryEmbedding == null || queryEmbedding.Length == 0)
            {
                _logger.LogWarning("Query embedding is null or empty");
                return results;
            }

            lock (_lockObject)
            {
                foreach (var kvp in _chunks)
                {
                    var chunk = kvp.Value;
                    
                    if (chunk.Embedding == null || chunk.Embedding.Length == 0)
                    {
                        _logger.LogWarning("Chunk {ChunkId} has no embedding", chunk.Id);
                        continue;
                    }

                    if (chunk.Embedding.Length != queryEmbedding.Length)
                    {
                        _logger.LogWarning("Embedding dimension mismatch for chunk {ChunkId}: expected {Expected}, got {Actual}", 
                            chunk.Id, queryEmbedding.Length, chunk.Embedding.Length);
                        continue;
                    }

                    var similarity = CalculateCosineSimilarity(queryEmbedding, chunk.Embedding);
                    
                    if (similarity >= threshold)
                    {
                        results.Add(new SimilarityResult
                        {
                            Chunk = chunk,
                            Similarity = similarity
                        });
                    }
                }
            }

            // Sort by similarity (descending) and take top K
            var topResults = results
                .OrderByDescending(r => r.Similarity)
                .Take(topK)
                .ToList();

            // Add rank information
            for (int i = 0; i < topResults.Count; i++)
            {
                topResults[i].Rank = i + 1;
            }

            _logger.LogDebug("Found {ResultCount} similar chunks (threshold: {Threshold}, topK: {TopK})", 
                topResults.Count, threshold, topK);

            return topResults;
        });
    }

    public async Task RemoveChunksBySourceAsync(string sourceFile)
    {
        await Task.Run(() =>
        {
            var chunksToRemove = _chunks.Values
                .Where(c => c.SourceFile.Equals(sourceFile, StringComparison.OrdinalIgnoreCase))
                .Select(c => c.Id)
                .ToList();

            foreach (var chunkId in chunksToRemove)
            {
                _chunks.TryRemove(chunkId, out _);
            }

            _logger.LogInformation("Removed {ChunkCount} chunks from source file {SourceFile}", 
                chunksToRemove.Count, sourceFile);
        });
    }

    public async Task ClearAsync()
    {
        await Task.Run(() =>
        {
            var count = _chunks.Count;
            _chunks.Clear();
            _logger.LogInformation("Cleared {ChunkCount} chunks from vector store", count);
        });
    }

    public async Task<int> GetChunkCountAsync()
    {
        return await Task.FromResult(_chunks.Count);
    }

    public async Task<List<DocumentChunk>> GetAllChunksAsync()
    {
        return await Task.FromResult(_chunks.Values.ToList());
    }

    private static float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
        {
            throw new ArgumentException("Vectors must have the same length");
        }

        try
        {
            // Use System.Numerics.Tensors for efficient computation
            var span1 = vector1.AsSpan();
            var span2 = vector2.AsSpan();

            var dotProduct = TensorPrimitives.Dot(span1, span2);
            var magnitude1 = Math.Sqrt(TensorPrimitives.Dot(span1, span1));
            var magnitude2 = Math.Sqrt(TensorPrimitives.Dot(span2, span2));

            if (magnitude1 == 0 || magnitude2 == 0)
            {
                return 0f;
            }

            return (float)(dotProduct / (magnitude1 * magnitude2));
        }
        catch
        {
            // Fallback to manual calculation if tensors fail
            return CalculateCosineSimilarityManual(vector1, vector2);
        }
    }

    private static float CalculateCosineSimilarityManual(float[] vector1, float[] vector2)
    {
        double dotProduct = 0;
        double magnitude1 = 0;
        double magnitude2 = 0;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = Math.Sqrt(magnitude1);
        magnitude2 = Math.Sqrt(magnitude2);

        if (magnitude1 == 0 || magnitude2 == 0)
        {
            return 0f;
        }

        return (float)(dotProduct / (magnitude1 * magnitude2));
    }
}

// Alternative implementation using approximate nearest neighbor search for large datasets
public class ApproximateVectorStore : IVectorStore
{
    private readonly List<DocumentChunk> _chunks = new();
    private readonly ILogger<ApproximateVectorStore> _logger;
    private readonly object _lockObject = new();

    public ApproximateVectorStore(ILogger<ApproximateVectorStore> logger)
    {
        _logger = logger;
    }

    public async Task AddChunkAsync(DocumentChunk chunk)
    {
        await Task.Run(() =>
        {
            lock (_lockObject)
            {
                // Remove existing chunk with same ID if it exists
                _chunks.RemoveAll(c => c.Id == chunk.Id);
                _chunks.Add(chunk);
            }
            _logger.LogDebug("Added chunk {ChunkId} from {SourceFile}", chunk.Id, chunk.SourceFile);
        });
    }

    public async Task AddChunksAsync(IEnumerable<DocumentChunk> chunks)
    {
        await Task.Run(() =>
        {
            lock (_lockObject)
            {
                foreach (var chunk in chunks)
                {
                    _chunks.RemoveAll(c => c.Id == chunk.Id);
                    _chunks.Add(chunk);
                }
            }
            _logger.LogInformation("Added {ChunkCount} chunks to vector store", chunks.Count());
        });
    }

    public async Task<List<SimilarityResult>> SearchSimilarAsync(float[] queryEmbedding, int topK = 5, float threshold = 0.0f)
    {
        return await Task.Run(() =>
        {
            var results = new List<SimilarityResult>();

            if (queryEmbedding == null || queryEmbedding.Length == 0)
            {
                return results;
            }

            lock (_lockObject)
            {
                // For large datasets, you might want to implement:
                // - LSH (Locality Sensitive Hashing)
                // - Hierarchical Navigable Small World (HNSW)
                // - Product Quantization
                // For now, we'll use the same brute force approach as InMemoryVectorStore

                foreach (var chunk in _chunks)
                {
                    if (chunk.Embedding?.Length == queryEmbedding.Length)
                    {
                        var similarity = CalculateCosineSimilarity(queryEmbedding, chunk.Embedding);
                        if (similarity >= threshold)
                        {
                            results.Add(new SimilarityResult
                            {
                                Chunk = chunk,
                                Similarity = similarity
                            });
                        }
                    }
                }
            }

            var topResults = results
                .OrderByDescending(r => r.Similarity)
                .Take(topK)
                .ToList();

            for (int i = 0; i < topResults.Count; i++)
            {
                topResults[i].Rank = i + 1;
            }

            return topResults;
        });
    }

    public async Task RemoveChunksBySourceAsync(string sourceFile)
    {
        await Task.Run(() =>
        {
            lock (_lockObject)
            {
                var removedCount = _chunks.RemoveAll(c => 
                    c.SourceFile.Equals(sourceFile, StringComparison.OrdinalIgnoreCase));
                
                _logger.LogInformation("Removed {ChunkCount} chunks from source file {SourceFile}", 
                    removedCount, sourceFile);
            }
        });
    }

    public async Task ClearAsync()
    {
        await Task.Run(() =>
        {
            lock (_lockObject)
            {
                var count = _chunks.Count;
                _chunks.Clear();
                _logger.LogInformation("Cleared {ChunkCount} chunks from vector store", count);
            }
        });
    }

    public async Task<int> GetChunkCountAsync()
    {
        return await Task.FromResult(_chunks.Count);
    }

    public async Task<List<DocumentChunk>> GetAllChunksAsync()
    {
        lock (_lockObject)
        {
            return _chunks.ToList();
        }
    }

    private static float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        double dotProduct = 0;
        double magnitude1 = 0;
        double magnitude2 = 0;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = Math.Sqrt(magnitude1);
        magnitude2 = Math.Sqrt(magnitude2);

        if (magnitude1 == 0 || magnitude2 == 0)
        {
            return 0f;
        }

        return (float)(dotProduct / (magnitude1 * magnitude2));
    }
}
