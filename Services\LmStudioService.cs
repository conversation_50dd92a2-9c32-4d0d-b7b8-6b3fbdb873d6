using BlazorAiChat.Models;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;

namespace BlazorAiChat.Services;

public class LmStudioService : ILmStudioService
{
    private readonly HttpClient _httpClient;
    private readonly LmStudioSettings _settings;
    private readonly ILogger<LmStudioService> _logger;

    public LmStudioService(
        HttpClient httpClient, 
        IOptions<LmStudioSettings> settings, 
        ILogger<LmStudioService> logger)
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _logger = logger;
        
        // Configure HTTP client
        _httpClient.Timeout = TimeSpan.FromMinutes(5);
    }

    public async Task<string> GenerateResponseAsync(string prompt, string? context = null)
    {
        try
        {
            var messages = new List<LmStudioMessage>();

            if (!string.IsNullOrEmpty(context))
            {
                messages.Add(new LmStudioMessage
                {
                    Role = "system",
                    Content = "You are a helpful AI assistant. Use the following context to answer questions accurately and concisely. If the context doesn't contain relevant information, say so clearly."
                });

                messages.Add(new LmStudioMessage
                {
                    Role = "user",
                    Content = $"Context:\n{context}\n\nQuestion: {prompt}"
                });
            }
            else
            {
                messages.Add(new LmStudioMessage
                {
                    Role = "user",
                    Content = prompt
                });
            }

            var request = new LmStudioChatRequest
            {
                Model = _settings.Model,
                Messages = messages,
                Temperature = _settings.Temperature,
                MaxTokens = _settings.MaxTokens,
                Stream = false
            };

            var response = await SendChatRequestAsync(request);
            
            if (response?.Choices?.Count > 0)
            {
                return response.Choices[0].Message.Content;
            }

            _logger.LogWarning("No response choices returned from LM Studio");
            return "I apologize, but I couldn't generate a response at this time.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating response from LM Studio");
            return "I apologize, but I encountered an error while processing your request.";
        }
    }

    public async Task<string> GenerateChatResponseAsync(List<ChatMessage> messages, string? context = null)
    {
        try
        {
            var lmStudioMessages = new List<LmStudioMessage>();

            if (!string.IsNullOrEmpty(context))
            {
                lmStudioMessages.Add(new LmStudioMessage
                {
                    Role = "system",
                    Content = $"You are a helpful AI assistant. Use the following context to answer questions:\n\n{context}"
                });
            }

            // Convert chat messages to LM Studio format
            foreach (var message in messages.Where(m => m.Role != ChatRole.System))
            {
                lmStudioMessages.Add(new LmStudioMessage
                {
                    Role = message.Role.ToString().ToLowerInvariant(),
                    Content = message.Content
                });
            }

            var request = new LmStudioChatRequest
            {
                Model = _settings.Model,
                Messages = lmStudioMessages,
                Temperature = _settings.Temperature,
                MaxTokens = _settings.MaxTokens,
                Stream = false
            };

            var response = await SendChatRequestAsync(request);
            
            if (response?.Choices?.Count > 0)
            {
                return response.Choices[0].Message.Content;
            }

            _logger.LogWarning("No response choices returned from LM Studio");
            return "I apologize, but I couldn't generate a response at this time.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating chat response from LM Studio");
            return "I apologize, but I encountered an error while processing your request.";
        }
    }

    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            _logger.LogDebug("Checking LM Studio availability at {BaseUrl}", _settings.BaseUrl);
            
            var response = await _httpClient.GetAsync($"{_settings.BaseUrl}/v1/models");
            var isAvailable = response.IsSuccessStatusCode;
            
            _logger.LogDebug("LM Studio availability check: {IsAvailable}", isAvailable);
            return isAvailable;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "LM Studio is not available at {BaseUrl}", _settings.BaseUrl);
            return false;
        }
    }

    public async Task<string> GetModelInfoAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_settings.BaseUrl}/v1/models");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var modelsResponse = JsonSerializer.Deserialize<JsonElement>(content);
                
                if (modelsResponse.TryGetProperty("data", out var dataElement) && dataElement.ValueKind == JsonValueKind.Array)
                {
                    var models = dataElement.EnumerateArray()
                        .Select(model => model.GetProperty("id").GetString())
                        .Where(id => !string.IsNullOrEmpty(id))
                        .ToList();
                    
                    return $"Available models: {string.Join(", ", models)}";
                }
            }
            
            return "Could not retrieve model information";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting model info from LM Studio");
            return "Error retrieving model information";
        }
    }

    private async Task<LmStudioChatResponse?> SendChatRequestAsync(LmStudioChatRequest request)
    {
        try
        {
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
                WriteIndented = false
            };

            var json = JsonSerializer.Serialize(request, jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _logger.LogDebug("Sending request to LM Studio: {Url}", $"{_settings.BaseUrl}/v1/chat/completions");
            
            var response = await _httpClient.PostAsync($"{_settings.BaseUrl}/v1/chat/completions", content);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("LM Studio API error: {StatusCode} - {Content}", response.StatusCode, errorContent);
                return null;
            }

            var responseJson = await response.Content.ReadAsStringAsync();
            _logger.LogDebug("Received response from LM Studio: {Length} characters", responseJson.Length);

            var chatResponse = JsonSerializer.Deserialize<LmStudioChatResponse>(responseJson, jsonOptions);
            return chatResponse;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error communicating with LM Studio at {BaseUrl}", _settings.BaseUrl);
            throw new InvalidOperationException($"Could not connect to LM Studio at {_settings.BaseUrl}. Please ensure LM Studio is running and accessible.", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Request to LM Studio timed out");
            throw new InvalidOperationException("Request to LM Studio timed out. The model might be taking too long to respond.", ex);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Error parsing JSON response from LM Studio");
            throw new InvalidOperationException("Received invalid response from LM Studio.", ex);
        }
    }
}

// Mock implementation for testing without LM Studio
public class MockLmStudioService : ILmStudioService
{
    private readonly ILogger<MockLmStudioService> _logger;
    private readonly Random _random = new();

    public MockLmStudioService(ILogger<MockLmStudioService> logger)
    {
        _logger = logger;
    }

    public async Task<string> GenerateResponseAsync(string prompt, string? context = null)
    {
        await Task.Delay(_random.Next(500, 2000)); // Simulate processing time

        if (!string.IsNullOrEmpty(context))
        {
            return $"Based on the provided context, I can help answer your question about: {prompt.Substring(0, Math.Min(50, prompt.Length))}...\n\n" +
                   $"This is a mock response that would normally be generated by LM Studio. " +
                   $"The context contains information that would be used to provide a more accurate answer.";
        }

        return $"This is a mock response to: {prompt.Substring(0, Math.Min(50, prompt.Length))}...\n\n" +
               $"In a real implementation, this would be generated by LM Studio using the configured language model.";
    }

    public async Task<string> GenerateChatResponseAsync(List<ChatMessage> messages, string? context = null)
    {
        var lastMessage = messages.LastOrDefault(m => m.Role == ChatRole.User);
        return await GenerateResponseAsync(lastMessage?.Content ?? "Hello", context);
    }

    public async Task<bool> IsAvailableAsync()
    {
        await Task.Delay(100);
        return true; // Mock service is always available
    }

    public async Task<string> GetModelInfoAsync()
    {
        await Task.Delay(100);
        return "Mock LM Studio Service - Available models: mock-llama-3.2-3b, mock-mistral-7b";
    }
}
