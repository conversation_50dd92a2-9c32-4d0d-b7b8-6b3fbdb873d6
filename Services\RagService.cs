using BlazorAiChat.Models;
using Microsoft.Extensions.Options;

namespace BlazorAiChat.Services;

public class RagService : IRagService
{
    private readonly IEmbeddingService _embeddingService;
    private readonly IVectorStore _vectorStore;
    private readonly IDocumentProcessor _documentProcessor;
    private readonly ILmStudioService _lmStudioService;
    private readonly DocumentSettings _documentSettings;
    private readonly ILogger<RagService> _logger;
    private bool _isInitialized = false;

    public RagService(
        IEmbeddingService embeddingService,
        IVectorStore vectorStore,
        IDocumentProcessor documentProcessor,
        ILmStudioService lmStudioService,
        IOptions<DocumentSettings> documentSettings,
        ILogger<RagService> logger)
    {
        _embeddingService = embeddingService;
        _vectorStore = vectorStore;
        _documentProcessor = documentProcessor;
        _lmStudioService = lmStudioService;
        _documentSettings = documentSettings.Value;
        _logger = logger;
    }

    public bool IsInitialized => _isInitialized;

    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing RAG service...");

            // Initialize embedding service
            if (!_embeddingService.IsInitialized)
            {
                await _embeddingService.InitializeAsync();
            }

            // Load and process documents
            await ReloadDocumentsAsync();

            _isInitialized = true;
            _logger.LogInformation("RAG service initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize RAG service");
            throw;
        }
    }

    public async Task<string> GenerateResponseAsync(string query)
    {
        try
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
            }

            if (string.IsNullOrWhiteSpace(query))
            {
                return "Please provide a question or query.";
            }

            _logger.LogInformation("Processing query: {Query}", query.Substring(0, Math.Min(100, query.Length)));

            // Check if LM Studio is available
            var isLmStudioAvailable = await _lmStudioService.IsAvailableAsync();
            if (!isLmStudioAvailable)
            {
                _logger.LogWarning("LM Studio is not available");
                return "I'm sorry, but the AI service is currently unavailable. Please ensure LM Studio is running and try again.";
            }

            // Retrieve relevant document chunks
            var relevantChunks = await RetrieveRelevantChunksAsync(query, _documentSettings.MaxRetrievedChunks);

            if (!relevantChunks.Any())
            {
                _logger.LogInformation("No relevant chunks found for query");
                return await _lmStudioService.GenerateResponseAsync(
                    query, 
                    "No relevant documents were found in the knowledge base for this query. Please provide a general response based on your training data.");
            }

            // Format context from retrieved chunks
            var context = FormatContext(relevantChunks);
            _logger.LogDebug("Generated context with {ChunkCount} chunks, {ContextLength} characters", 
                relevantChunks.Count, context.Length);

            // Generate response using LM Studio
            var response = await _lmStudioService.GenerateResponseAsync(query, context);

            _logger.LogInformation("Generated response for query: {ResponseLength} characters", response.Length);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating response for query: {Query}", query);
            return "I apologize, but I encountered an error while processing your request. Please try again.";
        }
    }

    public async Task<List<SimilarityResult>> RetrieveRelevantChunksAsync(string query, int topK = 5)
    {
        try
        {
            if (!_embeddingService.IsInitialized)
            {
                await _embeddingService.InitializeAsync();
            }

            // Generate embedding for the query
            var queryEmbeddingResult = await _embeddingService.GenerateEmbeddingAsync(query);
            
            if (!queryEmbeddingResult.Success || queryEmbeddingResult.Embedding.Length == 0)
            {
                _logger.LogWarning("Failed to generate embedding for query: {Error}", queryEmbeddingResult.ErrorMessage);
                return new List<SimilarityResult>();
            }

            // Search for similar chunks
            var similarChunks = await _vectorStore.SearchSimilarAsync(
                queryEmbeddingResult.Embedding, 
                topK, 
                threshold: 0.1f); // Minimum similarity threshold

            _logger.LogDebug("Found {ChunkCount} relevant chunks for query", similarChunks.Count);
            return similarChunks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving relevant chunks for query: {Query}", query);
            return new List<SimilarityResult>();
        }
    }

    public async Task ReloadDocumentsAsync()
    {
        try
        {
            _logger.LogInformation("Reloading documents from {DocumentsPath}", _documentSettings.DocumentsPath);

            // Clear existing chunks
            await _vectorStore.ClearAsync();

            // Process all documents
            var chunks = await _documentProcessor.ProcessAllDocumentsAsync(_documentSettings.DocumentsPath);
            
            if (!chunks.Any())
            {
                _logger.LogWarning("No documents found or processed from {DocumentsPath}", _documentSettings.DocumentsPath);
                return;
            }

            _logger.LogInformation("Processing {ChunkCount} chunks for embedding generation", chunks.Count);

            // Generate embeddings for all chunks
            var chunksWithEmbeddings = new List<DocumentChunk>();
            var batchSize = 10; // Process in batches to avoid overwhelming the embedding service

            for (int i = 0; i < chunks.Count; i += batchSize)
            {
                var batch = chunks.Skip(i).Take(batchSize).ToList();
                var batchTexts = batch.Select(c => c.Content).ToList();
                
                var embeddingResults = await _embeddingService.GenerateEmbeddingsAsync(batchTexts);
                
                for (int j = 0; j < batch.Count && j < embeddingResults.Count; j++)
                {
                    if (embeddingResults[j].Success)
                    {
                        batch[j].Embedding = embeddingResults[j].Embedding;
                        chunksWithEmbeddings.Add(batch[j]);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to generate embedding for chunk {ChunkId}: {Error}", 
                            batch[j].Id, embeddingResults[j].ErrorMessage);
                    }
                }

                _logger.LogDebug("Processed batch {BatchNumber}/{TotalBatches}", 
                    (i / batchSize) + 1, (chunks.Count + batchSize - 1) / batchSize);
            }

            // Add chunks to vector store
            await _vectorStore.AddChunksAsync(chunksWithEmbeddings);

            _logger.LogInformation("Successfully loaded {ChunkCount} chunks with embeddings", chunksWithEmbeddings.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reloading documents");
            throw;
        }
    }

    public async Task<int> GetDocumentCountAsync()
    {
        return await _vectorStore.GetChunkCountAsync();
    }

    private string FormatContext(List<SimilarityResult> relevantChunks)
    {
        if (!relevantChunks.Any())
        {
            return string.Empty;
        }

        var contextBuilder = new System.Text.StringBuilder();
        contextBuilder.AppendLine("Relevant information from the knowledge base:");
        contextBuilder.AppendLine();

        for (int i = 0; i < relevantChunks.Count; i++)
        {
            var chunk = relevantChunks[i].Chunk;
            var similarity = relevantChunks[i].Similarity;

            contextBuilder.AppendLine($"[Document {i + 1}] (Source: {chunk.SourceFile}, Relevance: {similarity:F2})");
            contextBuilder.AppendLine(chunk.Content);
            contextBuilder.AppendLine();
        }

        contextBuilder.AppendLine("Please use this information to answer the user's question accurately. If the information doesn't fully address the question, please indicate what aspects you cannot answer based on the provided context.");

        return contextBuilder.ToString();
    }
}

// Extension methods for better usability
public static class RagServiceExtensions
{
    public static async Task<bool> IsReadyAsync(this IRagService ragService)
    {
        try
        {
            var documentCount = await ragService.GetDocumentCountAsync();
            return ragService.IsInitialized && documentCount > 0;
        }
        catch
        {
            return false;
        }
    }

    public static async Task<string> GetStatusAsync(this IRagService ragService)
    {
        try
        {
            if (!ragService.IsInitialized)
            {
                return "Not initialized";
            }

            var documentCount = await ragService.GetDocumentCountAsync();
            return $"Ready - {documentCount} document chunks loaded";
        }
        catch (Exception ex)
        {
            return $"Error: {ex.Message}";
        }
    }
}
