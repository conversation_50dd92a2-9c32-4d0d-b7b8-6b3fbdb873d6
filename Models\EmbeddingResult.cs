namespace BlazorAiChat.Models;

public class EmbeddingResult
{
    public float[] Embedding { get; set; } = Array.Empty<float>();
    public string Text { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}

public class SimilarityResult
{
    public DocumentChunk Chunk { get; set; } = new();
    public float Similarity { get; set; }
    public int Rank { get; set; }
}
