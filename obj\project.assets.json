{"version": 3, "targets": {"net8.0": {"itext/8.0.2": {"type": "package", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "1.1.0", "Microsoft.Extensions.DependencyModel": "1.1.0", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.NonGeneric": "4.3.0", "System.Diagnostics.Process": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Text.Encoding.CodePages": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "itext.commons": "8.0.2"}, "compile": {"lib/netstandard2.0/itext.barcodes.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.bouncy-castle-connector.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.forms.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.io.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.kernel.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.layout.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.pdfa.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.sign.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.styledxmlparser.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.svg.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/itext.barcodes.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.bouncy-castle-connector.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.forms.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.io.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.kernel.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.layout.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.pdfa.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.sign.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.styledxmlparser.dll": {"related": ".xml"}, "lib/netstandard2.0/itext.svg.dll": {"related": ".xml"}}}, "itext.commons/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "5.0.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netstandard2.0/itext.commons.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/itext.commons.dll": {"related": ".xml"}}}, "itext7/8.0.2": {"type": "package", "dependencies": {"itext": "8.0.2"}}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"type": "package", "dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyModel/1.1.0": {"type": "package", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "1.1.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}, "compile": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24515.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.ML.OnnxRuntime/1.16.3": {"type": "package", "dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.16.3"}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.props": {}, "build/netstandard2.0/Microsoft.ML.OnnxRuntime.targets": {}}, "runtimeTargets": {"runtimes/android/native/onnxruntime.aar": {"assetType": "native", "rid": "android"}, "runtimes/ios/native/onnxruntime.xcframework/Info.plist": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/coreml_provider_factory.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/cpu_provider_factory.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_c_api.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_cxx_api.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_cxx_inline.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_float16.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Info.plist": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/onnxruntime": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/coreml_provider_factory.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/cpu_provider_factory.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_c_api.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_cxx_api.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_cxx_inline.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_float16.h": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Info.plist": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/onnxruntime": {"assetType": "native", "rid": "ios"}, "runtimes/linux-arm64/native/libonnxruntime.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/native/libonnxruntime.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libonnxruntime.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libonnxruntime.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/onnxruntime.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/onnxruntime.lib": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/onnxruntime.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime.lib": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/onnxruntime.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/onnxruntime.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime.lib": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime_providers_shared.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/onnxruntime_providers_shared.lib": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.16.3": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/net6.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/Microsoft.ML.OnnxRuntime.dll": {"related": ".pdb"}}, "build": {"build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets": {}}}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "Microsoft.Win32.Registry/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "debian.8-x64"}}}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.23-x64"}}}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.24-x64"}}}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.13.2-x64"}}}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.42.1-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "rhel.7-x64"}}}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.14.04-x64"}}}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.04-x64"}}}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.10-x64"}}}, "System.AppContext/4.1.0": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {"related": ".xml"}}}, "System.Diagnostics.Process/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.4/System.Diagnostics.Process.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/linux/lib/netstandard1.4/System.Diagnostics.Process.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/netstandard1.4/System.Diagnostics.Process.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/netstandard1.4/System.Diagnostics.Process.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {"related": ".xml"}}}, "System.Globalization.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Expressions/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Numerics.Tensors/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Numerics.Tensors.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.ObjectModel/4.0.12": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.0.1": {"type": "package", "dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.TypeExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Runtime.Numerics/4.3.0": {"type": "package", "dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Formatters.dll": {}}, "runtime": {"lib/netstandard1.4/System.Runtime.Serialization.Formatters.dll": {}}}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Csp.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "dependencies": {"System.Runtime": "4.3.1"}, "compile": {"ref/netcoreapp1.1/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.Threading.Thread/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Thread.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.ThreadPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.ThreadPool.dll": {}}}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.ReaderWriter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XmlDocument.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {}}}}}, "libraries": {"itext/8.0.2": {"sha512": "79IRTeZZirzRDEtB1PtUMSpsxaJiFz5rAXY4/VtSipsi0qlQpJ/88jxd2SsfX0IggH+wlrsiAc0Y8X2et2T+Bg==", "type": "package", "path": "itext/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "ITSC-avatar.png", "LICENSE.md", "gnu-agpl-v3.0.md", "itext.8.0.2.nupkg.sha512", "itext.nuspec", "lib/net461/itext.barcodes.dll", "lib/net461/itext.barcodes.xml", "lib/net461/itext.bouncy-castle-connector.dll", "lib/net461/itext.bouncy-castle-connector.xml", "lib/net461/itext.forms.dll", "lib/net461/itext.forms.xml", "lib/net461/itext.io.dll", "lib/net461/itext.io.xml", "lib/net461/itext.kernel.dll", "lib/net461/itext.kernel.xml", "lib/net461/itext.layout.dll", "lib/net461/itext.layout.xml", "lib/net461/itext.pdfa.dll", "lib/net461/itext.pdfa.xml", "lib/net461/itext.sign.dll", "lib/net461/itext.sign.xml", "lib/net461/itext.styledxmlparser.dll", "lib/net461/itext.styledxmlparser.xml", "lib/net461/itext.svg.dll", "lib/net461/itext.svg.xml", "lib/netstandard2.0/itext.barcodes.dll", "lib/netstandard2.0/itext.barcodes.xml", "lib/netstandard2.0/itext.bouncy-castle-connector.dll", "lib/netstandard2.0/itext.bouncy-castle-connector.xml", "lib/netstandard2.0/itext.forms.dll", "lib/netstandard2.0/itext.forms.xml", "lib/netstandard2.0/itext.io.dll", "lib/netstandard2.0/itext.io.xml", "lib/netstandard2.0/itext.kernel.dll", "lib/netstandard2.0/itext.kernel.xml", "lib/netstandard2.0/itext.layout.dll", "lib/netstandard2.0/itext.layout.xml", "lib/netstandard2.0/itext.pdfa.dll", "lib/netstandard2.0/itext.pdfa.xml", "lib/netstandard2.0/itext.sign.dll", "lib/netstandard2.0/itext.sign.xml", "lib/netstandard2.0/itext.styledxmlparser.dll", "lib/netstandard2.0/itext.styledxmlparser.xml", "lib/netstandard2.0/itext.svg.dll", "lib/netstandard2.0/itext.svg.xml"]}, "itext.commons/8.0.2": {"sha512": "/F7j7XERA84XgIXDb4j/so1GagAToraVKZIRAdk/ibkvYoU1uwuILU2Ly28sSFf12NN7WIBQHKSk7njJl7Nk8g==", "type": "package", "path": "itext.commons/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "ITSC-avatar.png", "itext.commons.8.0.2.nupkg.sha512", "itext.commons.nuspec", "lib/net461/itext.commons.dll", "lib/net461/itext.commons.xml", "lib/netstandard2.0/itext.commons.dll", "lib/netstandard2.0/itext.commons.xml"]}, "itext7/8.0.2": {"sha512": "IuFfSlMSTLqVGCIsxpcDL9kCtEfppSBjq4V+RgfrxnqEC+iNF4tCHiIdl8quyzTDbaHF4vNufxe2NXMJMkPlSw==", "type": "package", "path": "itext7/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "ITSC-avatar.png", "LICENSE.md", "gnu-agpl-v3.0.md", "itext7.8.0.2.nupkg.sha512", "itext7.nuspec"]}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"sha512": "Bl6KYfbFSIW3QIRHAp931iR5h01qHjKghdpAtncwbzNUs0+IUZ+XfwkIU0sQsR33ufGvi3u4dZMIYYFysjpHAA==", "type": "package", "path": "microsoft.dotnet.platformabstractions/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.DotNet.PlatformAbstractions.dll", "lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll", "microsoft.dotnet.platformabstractions.1.1.0.nupkg.sha512", "microsoft.dotnet.platformabstractions.nuspec"]}, "Microsoft.Extensions.DependencyInjection/5.0.0": {"sha512": "Rc2kb/p3Ze6cP6rhFC3PJRdWGbLvSHZc0ev7YlyeU6FmHciDMLrhoVoTUEzKPhN5ZjFgKF1Cf5fOz8mCMIkvpA==", "type": "package", "path": "microsoft.extensions.dependencyinjection/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net5.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net5.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.5.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"sha512": "ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyModel/1.1.0": {"sha512": "TG7dJ8GY1Myz9lZ8DJL4i6D05ncJQBi5CjBMXMdJ4edKxaW+vP2DndDd1jJabdMdmVRdGrvybzqkB+A6Df7eDw==", "type": "package", "path": "microsoft.extensions.dependencymodel/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "microsoft.extensions.dependencymodel.1.1.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec"]}, "Microsoft.Extensions.Logging/5.0.0": {"sha512": "MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "type": "package", "path": "microsoft.extensions.logging/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.5.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"sha512": "NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "type": "package", "path": "microsoft.extensions.logging.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Options/5.0.0": {"sha512": "CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "type": "package", "path": "microsoft.extensions.options/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/net5.0/Microsoft.Extensions.Options.dll", "lib/net5.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.5.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Primitives/5.0.0": {"sha512": "cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "type": "package", "path": "microsoft.extensions.primitives/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Primitives.dll", "lib/net461/Microsoft.Extensions.Primitives.xml", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.5.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24515.1": {"sha512": "qkslflgwSnMi3AL3rvH949KaEkGce0GwzlKkamorMEgtz8WMgjSq78kQzrnCSIdc4rZ6xhqSCa2DU24IlFKkRA==", "type": "package", "path": "microsoft.extensions.vectordata.abstractions/9.0.0-preview.1.24515.1", "files": [".nupkg.metadata", ".signature.p7s", "NUGET.md", "PACKAGE.md", "icon.png", "lib/net462/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/net462/Microsoft.Extensions.VectorData.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.VectorData.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.VectorData.Abstractions.xml", "microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.24515.1.nupkg.sha512", "microsoft.extensions.vectordata.abstractions.nuspec", "neticon.png"]}, "Microsoft.ML.OnnxRuntime/1.16.3": {"sha512": "N/FLnuU8TFxn4DGl7t5Nb2fTo62FtXPbCOPXQHWNWZ/AiUghmV7J6tuGkP89NOOd5Fe4FVmbmXyc8EbiNj2ZCA==", "type": "package", "path": "microsoft.ml.onnxruntime/1.16.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ORT_icon_for_light_bg.png", "Privacy.md", "README.md", "ThirdPartyNotices.txt", "build/monoandroid11.0/Microsoft.ML.OnnxRuntime.targets", "build/native/Microsoft.ML.OnnxRuntime.props", "build/native/Microsoft.ML.OnnxRuntime.targets", "build/native/include/cpu_provider_factory.h", "build/native/include/onnxruntime_c_api.h", "build/native/include/onnxruntime_cxx_api.h", "build/native/include/onnxruntime_cxx_inline.h", "build/native/include/onnxruntime_float16.h", "build/native/include/onnxruntime_lite_custom_op.h", "build/native/include/onnxruntime_run_options_config_keys.h", "build/native/include/onnxruntime_session_options_config_keys.h", "build/native/include/provider_options.h", "build/net6.0-android31.0/Microsoft.ML.OnnxRuntime.targets", "build/net6.0-ios15.4/Microsoft.ML.OnnxRuntime.targets", "build/net6.0-macos12.3/Microsoft.ML.OnnxRuntime.targets", "build/netstandard1.1/Microsoft.ML.OnnxRuntime.props", "build/netstandard1.1/Microsoft.ML.OnnxRuntime.targets", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.props", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.targets", "build/xamarinios10/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/monoandroid11.0/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net6.0-android31.0/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net6.0-ios15.4/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/net6.0-macos12.3/Microsoft.ML.OnnxRuntime.targets", "buildTransitive/xamarinios10/Microsoft.ML.OnnxRuntime.targets", "microsoft.ml.onnxruntime.1.16.3.nupkg.sha512", "microsoft.ml.onnxruntime.nuspec", "runtimes/android/native/onnxruntime.aar", "runtimes/ios/native/onnxruntime.xcframework/Info.plist", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/coreml_provider_factory.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/cpu_provider_factory.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_c_api.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_cxx_api.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_cxx_inline.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Headers/onnxruntime_float16.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/Info.plist", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64/onnxruntime.framework/onnxruntime", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/coreml_provider_factory.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/cpu_provider_factory.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_c_api.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_cxx_api.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_cxx_inline.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Headers/onnxruntime_float16.h", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/Info.plist", "runtimes/ios/native/onnxruntime.xcframework/ios-arm64_x86_64-simulator/onnxruntime.framework/onnxruntime", "runtimes/linux-arm64/native/libonnxruntime.so", "runtimes/linux-x64/native/libonnxruntime.so", "runtimes/osx-arm64/native/libonnxruntime.dylib", "runtimes/osx-x64/native/libonnxruntime.dylib", "runtimes/win-arm/native/onnxruntime.dll", "runtimes/win-arm/native/onnxruntime.lib", "runtimes/win-arm/native/onnxruntime_providers_shared.dll", "runtimes/win-arm/native/onnxruntime_providers_shared.lib", "runtimes/win-arm64/native/onnxruntime.dll", "runtimes/win-arm64/native/onnxruntime.lib", "runtimes/win-arm64/native/onnxruntime_providers_shared.dll", "runtimes/win-arm64/native/onnxruntime_providers_shared.lib", "runtimes/win-x64/native/onnxruntime.dll", "runtimes/win-x64/native/onnxruntime.lib", "runtimes/win-x64/native/onnxruntime_providers_shared.dll", "runtimes/win-x64/native/onnxruntime_providers_shared.lib", "runtimes/win-x86/native/onnxruntime.dll", "runtimes/win-x86/native/onnxruntime.lib", "runtimes/win-x86/native/onnxruntime_providers_shared.dll", "runtimes/win-x86/native/onnxruntime_providers_shared.lib"]}, "Microsoft.ML.OnnxRuntime.Managed/1.16.3": {"sha512": "w4uDtHgBvOQyJT51SndFZK2UiW7o4ecZOYFtvClJL0AqWXVtFGQBTA77Peb17AS1xMkjYoHV4uVyJRqEZYxZ3w==", "type": "package", "path": "microsoft.ml.onnxruntime.managed/1.16.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "ORT_icon_for_light_bg.png", "Privacy.md", "ThirdPartyNotices.txt", "build/netstandard2.0/Microsoft.ML.OnnxRuntime.Managed.targets", "lib/monoandroid11.0/Microsoft.ML.OnnxRuntime.dll", "lib/monoandroid11.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0-android31.0/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0-android31.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0-android31.0/Microsoft.ML.OnnxRuntime.xml", "lib/net6.0-ios16.1/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0-ios16.1/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0-macos13.0/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0-macos13.0/Microsoft.ML.OnnxRuntime.pdb", "lib/net6.0/Microsoft.ML.OnnxRuntime.dll", "lib/net6.0/Microsoft.ML.OnnxRuntime.pdb", "lib/netcoreapp3.1/Microsoft.ML.OnnxRuntime.dll", "lib/netcoreapp3.1/Microsoft.ML.OnnxRuntime.pdb", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.dll", "lib/netstandard2.0/Microsoft.ML.OnnxRuntime.pdb", "lib/xamarinios10/Microsoft.ML.OnnxRuntime.dll", "lib/xamarinios10/Microsoft.ML.OnnxRuntime.pdb", "microsoft.ml.onnxruntime.managed.1.16.3.nupkg.sha512", "microsoft.ml.onnxruntime.managed.nuspec"]}, "Microsoft.NETCore.Platforms/1.1.1": {"sha512": "TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "type": "package", "path": "microsoft.netcore.platforms/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.1.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.3": {"sha512": "3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "type": "package", "path": "microsoft.netcore.targets/1.1.3", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.3.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Primitives/4.3.0": {"sha512": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "type": "package", "path": "microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.3.0.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.Win32.Registry/4.3.0": {"sha512": "Lw1/VwLH1yxz6SfFEjVRCN0pnflLEsWgnV4qsdJ512/HhTwnKXUG+zDQ4yTO3K/EJQemGoNaBHX5InISNKTzUQ==", "type": "package", "path": "microsoft.win32.registry/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/Microsoft.Win32.Registry.dll", "microsoft.win32.registry.4.3.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "type": "package", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "type": "package", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "type": "package", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.native.System/4.3.0": {"sha512": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "type": "package", "path": "runtime.native.system/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.3.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "type": "package", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.native.system.security.cryptography.apple.nuspec"]}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "type": "package", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.native.system.security.cryptography.openssl.nuspec"]}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "type": "package", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "type": "package", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib"]}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "type": "package", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "type": "package", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "type": "package", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "type": "package", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "System.AppContext/4.1.0": {"sha512": "3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "type": "package", "path": "system.appcontext/4.1.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll", "system.appcontext.4.1.0.nupkg.sha512", "system.appcontext.nuspec"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Concurrent/4.3.0": {"sha512": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "type": "package", "path": "system.collections.concurrent/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.concurrent.4.3.0.nupkg.sha512", "system.collections.concurrent.nuspec"]}, "System.Collections.NonGeneric/4.3.0": {"sha512": "prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "type": "package", "path": "system.collections.nongeneric/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.NonGeneric.dll", "lib/netstandard1.3/System.Collections.NonGeneric.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.xml", "ref/netstandard1.3/de/System.Collections.NonGeneric.xml", "ref/netstandard1.3/es/System.Collections.NonGeneric.xml", "ref/netstandard1.3/fr/System.Collections.NonGeneric.xml", "ref/netstandard1.3/it/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ja/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ko/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ru/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hans/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hant/System.Collections.NonGeneric.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.nongeneric.4.3.0.nupkg.sha512", "system.collections.nongeneric.nuspec"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.Process/4.3.0": {"sha512": "J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "type": "package", "path": "system.diagnostics.process/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Diagnostics.Process.dll", "lib/net461/System.Diagnostics.Process.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Diagnostics.Process.dll", "ref/net461/System.Diagnostics.Process.dll", "ref/netstandard1.3/System.Diagnostics.Process.dll", "ref/netstandard1.3/System.Diagnostics.Process.xml", "ref/netstandard1.3/de/System.Diagnostics.Process.xml", "ref/netstandard1.3/es/System.Diagnostics.Process.xml", "ref/netstandard1.3/fr/System.Diagnostics.Process.xml", "ref/netstandard1.3/it/System.Diagnostics.Process.xml", "ref/netstandard1.3/ja/System.Diagnostics.Process.xml", "ref/netstandard1.3/ko/System.Diagnostics.Process.xml", "ref/netstandard1.3/ru/System.Diagnostics.Process.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Process.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Process.xml", "ref/netstandard1.4/System.Diagnostics.Process.dll", "ref/netstandard1.4/System.Diagnostics.Process.xml", "ref/netstandard1.4/de/System.Diagnostics.Process.xml", "ref/netstandard1.4/es/System.Diagnostics.Process.xml", "ref/netstandard1.4/fr/System.Diagnostics.Process.xml", "ref/netstandard1.4/it/System.Diagnostics.Process.xml", "ref/netstandard1.4/ja/System.Diagnostics.Process.xml", "ref/netstandard1.4/ko/System.Diagnostics.Process.xml", "ref/netstandard1.4/ru/System.Diagnostics.Process.xml", "ref/netstandard1.4/zh-hans/System.Diagnostics.Process.xml", "ref/netstandard1.4/zh-hant/System.Diagnostics.Process.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/linux/lib/netstandard1.4/System.Diagnostics.Process.dll", "runtimes/osx/lib/netstandard1.4/System.Diagnostics.Process.dll", "runtimes/win/lib/net46/System.Diagnostics.Process.dll", "runtimes/win/lib/net461/System.Diagnostics.Process.dll", "runtimes/win/lib/netstandard1.4/System.Diagnostics.Process.dll", "runtimes/win7/lib/netcore50/_._", "system.diagnostics.process.4.3.0.nupkg.sha512", "system.diagnostics.process.nuspec"]}, "System.Diagnostics.Tracing/4.3.0": {"sha512": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "type": "package", "path": "system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.3.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Dynamic.Runtime/4.0.11": {"sha512": "db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "type": "package", "path": "system.dynamic.runtime/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Dynamic.Runtime.dll", "lib/netstandard1.3/System.Dynamic.Runtime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Dynamic.Runtime.dll", "ref/netcore50/System.Dynamic.Runtime.xml", "ref/netcore50/de/System.Dynamic.Runtime.xml", "ref/netcore50/es/System.Dynamic.Runtime.xml", "ref/netcore50/fr/System.Dynamic.Runtime.xml", "ref/netcore50/it/System.Dynamic.Runtime.xml", "ref/netcore50/ja/System.Dynamic.Runtime.xml", "ref/netcore50/ko/System.Dynamic.Runtime.xml", "ref/netcore50/ru/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hans/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.0/System.Dynamic.Runtime.dll", "ref/netstandard1.0/System.Dynamic.Runtime.xml", "ref/netstandard1.0/de/System.Dynamic.Runtime.xml", "ref/netstandard1.0/es/System.Dynamic.Runtime.xml", "ref/netstandard1.0/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.0/it/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.3/System.Dynamic.Runtime.dll", "ref/netstandard1.3/System.Dynamic.Runtime.xml", "ref/netstandard1.3/de/System.Dynamic.Runtime.xml", "ref/netstandard1.3/es/System.Dynamic.Runtime.xml", "ref/netstandard1.3/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.3/it/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Dynamic.Runtime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Dynamic.Runtime.dll", "system.dynamic.runtime.4.0.11.nupkg.sha512", "system.dynamic.runtime.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.Globalization.Extensions/4.3.0": {"sha512": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "type": "package", "path": "system.globalization.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Extensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.xml", "ref/netstandard1.3/de/System.Globalization.Extensions.xml", "ref/netstandard1.3/es/System.Globalization.Extensions.xml", "ref/netstandard1.3/fr/System.Globalization.Extensions.xml", "ref/netstandard1.3/it/System.Globalization.Extensions.xml", "ref/netstandard1.3/ja/System.Globalization.Extensions.xml", "ref/netstandard1.3/ko/System.Globalization.Extensions.xml", "ref/netstandard1.3/ru/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll", "runtimes/win/lib/net46/System.Globalization.Extensions.dll", "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll", "system.globalization.extensions.4.3.0.nupkg.sha512", "system.globalization.extensions.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.FileSystem/4.3.0": {"sha512": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "type": "package", "path": "system.io.filesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.3.0.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.3.0": {"sha512": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "type": "package", "path": "system.io.filesystem.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.3.0.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Expressions/4.1.0": {"sha512": "I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "type": "package", "path": "system.linq.expressions/4.1.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.1.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Tensors/8.0.0": {"sha512": "fhODzTe9ON9IzmRfyVeA6L8yXOciMtpq1YufkRVBliggcVKZE+XDxqIn46+yF4PWR6wNPuDpXtPpuY86VcKxUA==", "type": "package", "path": "system.numerics.tensors/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Numerics.Tensors.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Numerics.Tensors.targets", "lib/net462/System.Numerics.Tensors.dll", "lib/net462/System.Numerics.Tensors.xml", "lib/net6.0/System.Numerics.Tensors.dll", "lib/net6.0/System.Numerics.Tensors.xml", "lib/net7.0/System.Numerics.Tensors.dll", "lib/net7.0/System.Numerics.Tensors.xml", "lib/net8.0/System.Numerics.Tensors.dll", "lib/net8.0/System.Numerics.Tensors.xml", "lib/netstandard2.0/System.Numerics.Tensors.dll", "lib/netstandard2.0/System.Numerics.Tensors.xml", "system.numerics.tensors.8.0.0.nupkg.sha512", "system.numerics.tensors.nuspec", "useSharedDesignerContext.txt"]}, "System.ObjectModel/4.0.12": {"sha512": "tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "type": "package", "path": "system.objectmodel/4.0.12", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.0.12.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.0.1": {"sha512": "P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "type": "package", "path": "system.reflection.emit/4.0.1", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinmac20/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.0.1.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.0.1": {"sha512": "Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.0.1", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.0.1": {"sha512": "sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "type": "package", "path": "system.reflection.emit.lightweight/4.0.1", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.0.1.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.0.1": {"sha512": "GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "type": "package", "path": "system.reflection.extensions/4.0.1", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.0.1.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.1.0": {"sha512": "tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "type": "package", "path": "system.reflection.typeextensions/4.1.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.1.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.1": {"sha512": "abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "type": "package", "path": "system.runtime/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.1.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"sha512": "hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Runtime.Numerics/4.3.0": {"sha512": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "type": "package", "path": "system.runtime.numerics/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Numerics.dll", "lib/netstandard1.3/System.Runtime.Numerics.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Numerics.dll", "ref/netcore50/System.Runtime.Numerics.xml", "ref/netcore50/de/System.Runtime.Numerics.xml", "ref/netcore50/es/System.Runtime.Numerics.xml", "ref/netcore50/fr/System.Runtime.Numerics.xml", "ref/netcore50/it/System.Runtime.Numerics.xml", "ref/netcore50/ja/System.Runtime.Numerics.xml", "ref/netcore50/ko/System.Runtime.Numerics.xml", "ref/netcore50/ru/System.Runtime.Numerics.xml", "ref/netcore50/zh-hans/System.Runtime.Numerics.xml", "ref/netcore50/zh-hant/System.Runtime.Numerics.xml", "ref/netstandard1.1/System.Runtime.Numerics.dll", "ref/netstandard1.1/System.Runtime.Numerics.xml", "ref/netstandard1.1/de/System.Runtime.Numerics.xml", "ref/netstandard1.1/es/System.Runtime.Numerics.xml", "ref/netstandard1.1/fr/System.Runtime.Numerics.xml", "ref/netstandard1.1/it/System.Runtime.Numerics.xml", "ref/netstandard1.1/ja/System.Runtime.Numerics.xml", "ref/netstandard1.1/ko/System.Runtime.Numerics.xml", "ref/netstandard1.1/ru/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hans/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hant/System.Runtime.Numerics.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.numerics.4.3.0.nupkg.sha512", "system.runtime.numerics.nuspec"]}, "System.Runtime.Serialization.Formatters/4.3.0": {"sha512": "KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "type": "package", "path": "system.runtime.serialization.formatters/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Runtime.Serialization.Formatters.dll", "lib/netstandard1.4/System.Runtime.Serialization.Formatters.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Runtime.Serialization.Formatters.dll", "ref/netstandard1.3/System.Runtime.Serialization.Formatters.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "system.runtime.serialization.formatters.nuspec"]}, "System.Runtime.Serialization.Primitives/4.3.0": {"sha512": "Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "type": "package", "path": "system.runtime.serialization.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Primitives.dll", "lib/netcore50/System.Runtime.Serialization.Primitives.dll", "lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/de/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/es/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/fr/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/it/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ja/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ko/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ru/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.Serialization.Primitives.dll", "system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "system.runtime.serialization.primitives.nuspec"]}, "System.Security.Cryptography.Algorithms/4.3.0": {"sha512": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "type": "package", "path": "system.security.cryptography.algorithms/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Csp/4.3.0": {"sha512": "X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "type": "package", "path": "system.security.cryptography.csp/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Csp.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Csp.dll", "ref/netstandard1.3/System.Security.Cryptography.Csp.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "system.security.cryptography.csp.4.3.0.nupkg.sha512", "system.security.cryptography.csp.nuspec"]}, "System.Security.Cryptography.Encoding/4.3.0": {"sha512": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "type": "package", "path": "system.security.cryptography.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.3.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.Primitives/4.3.0": {"sha512": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "type": "package", "path": "system.security.cryptography.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.3.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/4.3.0": {"sha512": "IRiEFUa5b/Gs5Egg8oqBVoywhtOeaO2KOx3j0RfcYY/raxqBuEK7NXRDgOwtYM8qbi+7S4RPXUbNt+ZxyY0/NQ==", "type": "package", "path": "system.text.encoding.codepages/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.3/System.Text.Encoding.CodePages.dll", "ref/netstandard1.3/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/de/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/es/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/fr/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/it/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ja/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ko/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ru/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.CodePages.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "system.text.encoding.codepages.4.3.0.nupkg.sha512", "system.text.encoding.codepages.nuspec"]}, "System.Text.Encoding.Extensions/4.3.0": {"sha512": "YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "type": "package", "path": "system.text.encoding.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.3.0.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.RegularExpressions/4.3.1": {"sha512": "N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "type": "package", "path": "system.text.regularexpressions/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netcoreapp1.1/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.regularexpressions.4.3.1.nupkg.sha512", "system.text.regularexpressions.nuspec"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.3.0": {"sha512": "npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "type": "package", "path": "system.threading.tasks.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "system.threading.tasks.extensions.4.3.0.nupkg.sha512", "system.threading.tasks.extensions.nuspec"]}, "System.Threading.Thread/4.3.0": {"sha512": "OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "type": "package", "path": "system.threading.thread/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Threading.Thread.dll", "lib/netcore50/_._", "lib/netstandard1.3/System.Threading.Thread.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Threading.Thread.dll", "ref/netstandard1.3/System.Threading.Thread.dll", "ref/netstandard1.3/System.Threading.Thread.xml", "ref/netstandard1.3/de/System.Threading.Thread.xml", "ref/netstandard1.3/es/System.Threading.Thread.xml", "ref/netstandard1.3/fr/System.Threading.Thread.xml", "ref/netstandard1.3/it/System.Threading.Thread.xml", "ref/netstandard1.3/ja/System.Threading.Thread.xml", "ref/netstandard1.3/ko/System.Threading.Thread.xml", "ref/netstandard1.3/ru/System.Threading.Thread.xml", "ref/netstandard1.3/zh-hans/System.Threading.Thread.xml", "ref/netstandard1.3/zh-hant/System.Threading.Thread.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.thread.4.3.0.nupkg.sha512", "system.threading.thread.nuspec"]}, "System.Threading.ThreadPool/4.3.0": {"sha512": "k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "type": "package", "path": "system.threading.threadpool/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Threading.ThreadPool.dll", "lib/netcore50/_._", "lib/netstandard1.3/System.Threading.ThreadPool.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Threading.ThreadPool.dll", "ref/netstandard1.3/System.Threading.ThreadPool.dll", "ref/netstandard1.3/System.Threading.ThreadPool.xml", "ref/netstandard1.3/de/System.Threading.ThreadPool.xml", "ref/netstandard1.3/es/System.Threading.ThreadPool.xml", "ref/netstandard1.3/fr/System.Threading.ThreadPool.xml", "ref/netstandard1.3/it/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ja/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ko/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ru/System.Threading.ThreadPool.xml", "ref/netstandard1.3/zh-hans/System.Threading.ThreadPool.xml", "ref/netstandard1.3/zh-hant/System.Threading.ThreadPool.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.threadpool.4.3.0.nupkg.sha512", "system.threading.threadpool.nuspec"]}, "System.Xml.ReaderWriter/4.3.0": {"sha512": "GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "type": "package", "path": "system.xml.readerwriter/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Xml.ReaderWriter.dll", "lib/netcore50/System.Xml.ReaderWriter.dll", "lib/netstandard1.3/System.Xml.ReaderWriter.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.xml", "ref/netcore50/de/System.Xml.ReaderWriter.xml", "ref/netcore50/es/System.Xml.ReaderWriter.xml", "ref/netcore50/fr/System.Xml.ReaderWriter.xml", "ref/netcore50/it/System.Xml.ReaderWriter.xml", "ref/netcore50/ja/System.Xml.ReaderWriter.xml", "ref/netcore50/ko/System.Xml.ReaderWriter.xml", "ref/netcore50/ru/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hans/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/System.Xml.ReaderWriter.dll", "ref/netstandard1.0/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/System.Xml.ReaderWriter.dll", "ref/netstandard1.3/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hant/System.Xml.ReaderWriter.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.readerwriter.4.3.0.nupkg.sha512", "system.xml.readerwriter.nuspec"]}, "System.Xml.XmlDocument/4.3.0": {"sha512": "lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "type": "package", "path": "system.xml.xmldocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XmlDocument.dll", "lib/netstandard1.3/System.Xml.XmlDocument.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.xml", "ref/netstandard1.3/de/System.Xml.XmlDocument.xml", "ref/netstandard1.3/es/System.Xml.XmlDocument.xml", "ref/netstandard1.3/fr/System.Xml.XmlDocument.xml", "ref/netstandard1.3/it/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ja/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ko/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ru/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlDocument.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xmldocument.4.3.0.nupkg.sha512", "system.xml.xmldocument.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.Extensions.VectorData.Abstractions >= 8.0.0-preview.1.24570.5", "Microsoft.ML.OnnxRuntime >= 1.16.3", "Microsoft.ML.OnnxRuntime.Managed >= 1.16.3", "System.Numerics.Tensors >= 8.0.0", "itext7 >= 8.0.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Development\\FR Knowladge Base AI Chat Bot\\BlazorAiChat.csproj", "projectName": "BlazorAiChat", "projectPath": "C:\\Development\\FR Knowladge Base AI Chat Bot\\BlazorAiChat.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Development\\FR Knowladge Base AI Chat Bot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 19.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 19.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.VectorData.Abstractions": {"target": "Package", "version": "[8.0.0-preview.1.24570.5, )"}, "Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.16.3, )"}, "Microsoft.ML.OnnxRuntime.Managed": {"target": "Package", "version": "[1.16.3, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[8.0.0, )"}, "itext7": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1603", "level": "Warning", "warningLevel": 1, "message": "BlazorAiChat depends on Microsoft.Extensions.VectorData.Abstractions (>= 8.0.0-preview.1.24570.5) but Microsoft.Extensions.VectorData.Abstractions 8.0.0-preview.1.24570.5 was not found. Microsoft.Extensions.VectorData.Abstractions 9.0.0-preview.1.24515.1 was resolved instead.", "libraryId": "Microsoft.Extensions.VectorData.Abstractions", "targetGraphs": ["net8.0"]}]}