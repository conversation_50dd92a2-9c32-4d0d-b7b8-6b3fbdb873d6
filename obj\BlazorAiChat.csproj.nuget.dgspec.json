{"format": 1, "restore": {"C:\\Development\\FR Knowladge Base AI Chat Bot\\BlazorAiChat.csproj": {}}, "projects": {"C:\\Development\\FR Knowladge Base AI Chat Bot\\BlazorAiChat.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Development\\FR Knowladge Base AI Chat Bot\\BlazorAiChat.csproj", "projectName": "BlazorAiChat", "projectPath": "C:\\Development\\FR Knowladge Base AI Chat Bot\\BlazorAiChat.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Development\\FR Knowladge Base AI Chat Bot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 19.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 19.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.VectorData.Abstractions": {"target": "Package", "version": "[8.0.0-preview.1.24570.5, )"}, "Microsoft.ML.OnnxRuntime": {"target": "Package", "version": "[1.16.3, )"}, "Microsoft.ML.OnnxRuntime.Managed": {"target": "Package", "version": "[1.16.3, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[8.0.0, )"}, "itext7": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}