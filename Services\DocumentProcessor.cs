using BlazorAiChat.Models;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser;
using iText.Kernel.Pdf.Canvas.Parser.Listener;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.RegularExpressions;

namespace BlazorAiChat.Services;

public class DocumentProcessor : IDocumentProcessor
{
    private readonly DocumentSettings _settings;
    private readonly ILogger<DocumentProcessor> _logger;

    public DocumentProcessor(IOptions<DocumentSettings> settings, ILogger<DocumentProcessor> logger)
    {
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<List<DocumentChunk>> ProcessDocumentAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("File not found: {FilePath}", filePath);
                return new List<DocumentChunk>();
            }

            if (!IsSupportedFileType(filePath))
            {
                _logger.LogWarning("Unsupported file type: {FilePath}", filePath);
                return new List<DocumentChunk>();
            }

            var text = await ExtractTextFromFileAsync(filePath);
            if (string.IsNullOrWhiteSpace(text))
            {
                _logger.LogWarning("No text extracted from file: {FilePath}", filePath);
                return new List<DocumentChunk>();
            }

            var chunks = SplitTextIntoChunks(text, _settings.ChunkSize, _settings.ChunkOverlap);
            var documentChunks = new List<DocumentChunk>();

            for (int i = 0; i < chunks.Count; i++)
            {
                var chunk = new DocumentChunk
                {
                    Content = chunks[i],
                    SourceFile = Path.GetFileName(filePath),
                    ChunkIndex = i,
                    Metadata = new Dictionary<string, object>
                    {
                        ["file_path"] = filePath,
                        ["file_size"] = new FileInfo(filePath).Length,
                        ["file_extension"] = Path.GetExtension(filePath),
                        ["chunk_length"] = chunks[i].Length
                    }
                };
                documentChunks.Add(chunk);
            }

            _logger.LogInformation("Processed {ChunkCount} chunks from {FilePath}", documentChunks.Count, filePath);
            return documentChunks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document: {FilePath}", filePath);
            return new List<DocumentChunk>();
        }
    }

    public async Task<List<DocumentChunk>> ProcessAllDocumentsAsync(string documentsPath)
    {
        var allChunks = new List<DocumentChunk>();

        if (!Directory.Exists(documentsPath))
        {
            _logger.LogWarning("Documents directory not found: {DocumentsPath}", documentsPath);
            return allChunks;
        }

        var supportedExtensions = new[] { ".txt", ".pdf" };
        var files = Directory.GetFiles(documentsPath, "*.*", SearchOption.AllDirectories)
            .Where(file => supportedExtensions.Contains(Path.GetExtension(file).ToLowerInvariant()))
            .ToList();

        _logger.LogInformation("Found {FileCount} supported files in {DocumentsPath}", files.Count, documentsPath);

        foreach (var file in files)
        {
            var chunks = await ProcessDocumentAsync(file);
            allChunks.AddRange(chunks);
        }

        _logger.LogInformation("Processed {TotalChunks} total chunks from {FileCount} files", allChunks.Count, files.Count);
        return allChunks;
    }

    public async Task<string> ExtractTextFromFileAsync(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();

        return extension switch
        {
            ".txt" => await File.ReadAllTextAsync(filePath, Encoding.UTF8),
            ".pdf" => await ExtractTextFromPdfAsync(filePath),
            _ => throw new NotSupportedException($"File type {extension} is not supported")
        };
    }

    private async Task<string> ExtractTextFromPdfAsync(string filePath)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var pdfReader = new PdfReader(filePath);
                using var pdfDocument = new PdfDocument(pdfReader);
                
                var text = new StringBuilder();
                for (int i = 1; i <= pdfDocument.GetNumberOfPages(); i++)
                {
                    var page = pdfDocument.GetPage(i);
                    var strategy = new SimpleTextExtractionStrategy();
                    var pageText = PdfTextExtractor.GetTextFromPage(page, strategy);
                    text.AppendLine(pageText);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from PDF: {FilePath}", filePath);
                return string.Empty;
            }
        });
    }

    public List<string> SplitTextIntoChunks(string text, int chunkSize = 1000, int overlap = 200)
    {
        if (string.IsNullOrWhiteSpace(text))
            return new List<string>();

        // Clean and normalize the text
        text = CleanText(text);

        var chunks = new List<string>();
        var sentences = SplitIntoSentences(text);
        
        var currentChunk = new StringBuilder();
        var currentLength = 0;

        foreach (var sentence in sentences)
        {
            var sentenceLength = sentence.Length;

            // If adding this sentence would exceed chunk size, finalize current chunk
            if (currentLength + sentenceLength > chunkSize && currentChunk.Length > 0)
            {
                chunks.Add(currentChunk.ToString().Trim());
                
                // Start new chunk with overlap
                var overlapText = GetOverlapText(currentChunk.ToString(), overlap);
                currentChunk.Clear();
                currentChunk.Append(overlapText);
                currentLength = overlapText.Length;
            }

            currentChunk.Append(sentence);
            currentLength += sentenceLength;
        }

        // Add the last chunk if it has content
        if (currentChunk.Length > 0)
        {
            chunks.Add(currentChunk.ToString().Trim());
        }

        return chunks.Where(c => !string.IsNullOrWhiteSpace(c)).ToList();
    }

    private string CleanText(string text)
    {
        // Remove excessive whitespace and normalize line breaks
        text = Regex.Replace(text, @"\s+", " ");
        text = Regex.Replace(text, @"[\r\n]+", "\n");
        return text.Trim();
    }

    private List<string> SplitIntoSentences(string text)
    {
        // Simple sentence splitting - can be improved with more sophisticated NLP
        var sentences = Regex.Split(text, @"(?<=[.!?])\s+")
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .ToList();

        return sentences;
    }

    private string GetOverlapText(string text, int overlapSize)
    {
        if (text.Length <= overlapSize)
            return text;

        // Try to find a good breaking point (end of sentence) within the overlap region
        var overlapText = text.Substring(text.Length - overlapSize);
        var lastSentenceEnd = overlapText.LastIndexOfAny(new[] { '.', '!', '?' });
        
        if (lastSentenceEnd > 0)
        {
            return overlapText.Substring(lastSentenceEnd + 1).Trim();
        }

        return overlapText;
    }

    public bool IsSupportedFileType(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension == ".txt" || extension == ".pdf";
    }
}
