using BlazorAiChat.Models;

namespace BlazorAiChat.Services;

public interface IDocumentProcessor
{
    Task<List<DocumentChunk>> ProcessDocumentAsync(string filePath);
    Task<List<DocumentChunk>> ProcessAllDocumentsAsync(string documentsPath);
    Task<string> ExtractTextFromFileAsync(string filePath);
    List<string> SplitTextIntoChunks(string text, int chunkSize = 1000, int overlap = 200);
    bool IsSupportedFileType(string filePath);
}
