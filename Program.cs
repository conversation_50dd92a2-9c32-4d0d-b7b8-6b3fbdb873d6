using BlazorAiChat.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();
builder.Services.AddControllers();

// Add HTTP client for LM Studio API
builder.Services.AddHttpClient();

// Register custom services
builder.Services.AddSingleton<IEmbeddingService, EmbeddingService>();
builder.Services.AddSingleton<IVectorStore, InMemoryVectorStore>();
builder.Services.AddSingleton<IDocumentProcessor, DocumentProcessor>();
builder.Services.AddSingleton<ILmStudioService, LmStudioService>();
builder.Services.AddSingleton<IRagService, RagService>();
builder.Services.AddSingleton<IDocumentWatcher, DocumentWatcher>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.MapRazorPages();
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");
app.MapControllers();

// Initialize document processing on startup
var documentWatcher = app.Services.GetRequiredService<IDocumentWatcher>();
await documentWatcher.StartAsync();

app.Run();
