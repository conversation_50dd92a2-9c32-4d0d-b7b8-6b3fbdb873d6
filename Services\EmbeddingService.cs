using BlazorAiChat.Models;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace BlazorAiChat.Services;

public class EmbeddingService : IEmbeddingService
{
    private readonly EmbeddingSettings _settings;
    private readonly ILogger<EmbeddingService> _logger;
    private readonly HttpClient _httpClient;
    private bool _isInitialized = false;

    public EmbeddingService(
        IOptions<EmbeddingSettings> settings, 
        ILogger<EmbeddingService> logger,
        HttpClient httpClient)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClient = httpClient;
    }

    public bool IsInitialized => _isInitialized;

    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing embedding service...");
            
            // For this implementation, we'll use a simple text-based embedding
            // In a production environment, you would load an actual embedding model
            // such as ONNX Runtime with a sentence transformer model
            
            _isInitialized = true;
            _logger.LogInformation("Embedding service initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize embedding service");
            throw;
        }
    }

    public async Task<EmbeddingResult> GenerateEmbeddingAsync(string text)
    {
        if (!_isInitialized)
        {
            await InitializeAsync();
        }

        try
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return new EmbeddingResult
                {
                    Success = false,
                    ErrorMessage = "Input text is empty or null",
                    Text = text
                };
            }

            // Normalize the text
            var normalizedText = NormalizeText(text);
            
            // Generate a simple embedding based on text characteristics
            // In production, replace this with actual embedding model inference
            var embedding = await GenerateSimpleEmbeddingAsync(normalizedText);

            return new EmbeddingResult
            {
                Embedding = embedding,
                Text = text,
                Success = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embedding for text: {Text}", text.Substring(0, Math.Min(100, text.Length)));
            return new EmbeddingResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                Text = text
            };
        }
    }

    public async Task<List<EmbeddingResult>> GenerateEmbeddingsAsync(IEnumerable<string> texts)
    {
        var results = new List<EmbeddingResult>();
        
        foreach (var text in texts)
        {
            var result = await GenerateEmbeddingAsync(text);
            results.Add(result);
        }

        return results;
    }

    private string NormalizeText(string text)
    {
        // Convert to lowercase
        text = text.ToLowerInvariant();
        
        // Remove extra whitespace
        text = Regex.Replace(text, @"\s+", " ");
        
        // Remove special characters but keep basic punctuation
        text = Regex.Replace(text, @"[^\w\s\.\,\!\?\-]", "");
        
        return text.Trim();
    }

    private async Task<float[]> GenerateSimpleEmbeddingAsync(string text)
    {
        // This is a simplified embedding generation for demonstration
        // In production, you would use a proper embedding model like:
        // - ONNX Runtime with sentence-transformers
        // - OpenAI embeddings API
        // - Local transformer models
        
        return await Task.Run(() =>
        {
            var embedding = new float[_settings.Dimension];
            var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            
            // Create a simple hash-based embedding
            var hash = text.GetHashCode();
            var random = new Random(hash);
            
            // Generate normalized random vector
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] = (float)(random.NextDouble() * 2.0 - 1.0);
            }
            
            // Add some semantic features based on text characteristics
            if (words.Length > 0)
            {
                // Length feature
                embedding[0] = Math.Min(words.Length / 100.0f, 1.0f);
                
                // Common word features
                var commonWords = new[] { "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by" };
                var commonWordCount = words.Count(w => commonWords.Contains(w.ToLower()));
                embedding[1] = Math.Min(commonWordCount / (float)words.Length, 1.0f);
                
                // Question feature
                embedding[2] = text.Contains('?') ? 1.0f : 0.0f;
                
                // Technical terms feature (simple heuristic)
                var techWords = new[] { "api", "service", "data", "model", "system", "application", "code", "function" };
                var techWordCount = words.Count(w => techWords.Contains(w.ToLower()));
                embedding[3] = Math.Min(techWordCount / (float)words.Length, 1.0f);
            }
            
            // Normalize the vector
            var magnitude = Math.Sqrt(embedding.Sum(x => x * x));
            if (magnitude > 0)
            {
                for (int i = 0; i < embedding.Length; i++)
                {
                    embedding[i] = (float)(embedding[i] / magnitude);
                }
            }
            
            return embedding;
        });
    }
}

// Alternative implementation using OpenAI-compatible embedding API
public class OpenAiEmbeddingService : IEmbeddingService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<OpenAiEmbeddingService> _logger;
    private readonly string _baseUrl;
    private readonly string _model;
    private bool _isInitialized = false;

    public OpenAiEmbeddingService(HttpClient httpClient, ILogger<OpenAiEmbeddingService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _baseUrl = configuration["OpenAI:BaseUrl"] ?? "https://api.openai.com/v1";
        _model = configuration["OpenAI:EmbeddingModel"] ?? "text-embedding-ada-002";
    }

    public bool IsInitialized => _isInitialized;

    public async Task InitializeAsync()
    {
        _isInitialized = true;
        await Task.CompletedTask;
    }

    public async Task<EmbeddingResult> GenerateEmbeddingAsync(string text)
    {
        try
        {
            var request = new
            {
                input = text,
                model = _model
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/embeddings", content);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync();
            var embeddingResponse = JsonSerializer.Deserialize<JsonElement>(responseJson);

            var embeddingArray = embeddingResponse
                .GetProperty("data")[0]
                .GetProperty("embedding")
                .EnumerateArray()
                .Select(x => (float)x.GetDouble())
                .ToArray();

            return new EmbeddingResult
            {
                Embedding = embeddingArray,
                Text = text,
                Success = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embedding via OpenAI API");
            return new EmbeddingResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                Text = text
            };
        }
    }

    public async Task<List<EmbeddingResult>> GenerateEmbeddingsAsync(IEnumerable<string> texts)
    {
        var results = new List<EmbeddingResult>();
        
        foreach (var text in texts)
        {
            var result = await GenerateEmbeddingAsync(text);
            results.Add(result);
        }

        return results;
    }
}
