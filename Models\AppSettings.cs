namespace BlazorAiChat.Models;

public class LmStudioSettings
{
    public string BaseUrl { get; set; } = "http://localhost:1234";
    public string Model { get; set; } = "llama-3.2-3b-instruct";
    public int MaxTokens { get; set; } = 1000;
    public float Temperature { get; set; } = 0.7f;
}

public class DocumentSettings
{
    public string DocumentsPath { get; set; } = "Documents";
    public int ChunkSize { get; set; } = 1000;
    public int ChunkOverlap { get; set; } = 200;
    public int MaxRetrievedChunks { get; set; } = 5;
}

public class EmbeddingSettings
{
    public string ModelPath { get; set; } = "Models/all-MiniLM-L6-v2.onnx";
    public string VocabPath { get; set; } = "Models/vocab.txt";
    public int Dimension { get; set; } = 384;
}
