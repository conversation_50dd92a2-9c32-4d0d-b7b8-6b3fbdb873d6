using BlazorAiChat.Models;

namespace BlazorAiChat.Services;

public interface IVectorStore
{
    Task AddChunkAsync(DocumentChunk chunk);
    Task AddChunksAsync(IEnumerable<DocumentChunk> chunks);
    Task<List<SimilarityResult>> SearchSimilarAsync(float[] queryEmbedding, int topK = 5, float threshold = 0.0f);
    Task RemoveChunksBySourceAsync(string sourceFile);
    Task ClearAsync();
    Task<int> GetChunkCountAsync();
    Task<List<DocumentChunk>> GetAllChunksAsync();
}
