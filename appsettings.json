{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "LmStudio": {"BaseUrl": "http://localhost:1234", "Model": "llama-3.2-3b-instruct", "MaxTokens": 1000, "Temperature": 0.7}, "DocumentSettings": {"DocumentsPath": "Documents", "ChunkSize": 1000, "ChunkOverlap": 200, "MaxRetrievedChunks": 5}, "Embedding": {"ModelPath": "Models/all-MiniLM-L6-v2.onnx", "VocabPath": "Models/vocab.txt", "Dimension": 384}}