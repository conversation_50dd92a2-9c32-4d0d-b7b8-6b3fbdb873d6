Welcome to Blazor AI Chat

This is a sample document that demonstrates the RAG (Retrieval-Augmented Generation) capabilities of our Blazor AI Chat application.

What is RAG?
RAG stands for Retrieval-Augmented Generation. It's a technique that combines information retrieval with text generation to provide more accurate and contextual responses. The system works by:

1. Indexing documents: Documents are processed, split into chunks, and converted into vector embeddings
2. Retrieving relevant information: When a user asks a question, the system finds the most relevant document chunks
3. Generating responses: The retrieved information is used as context for the language model to generate accurate answers

Features of this application:
- Real-time chat interface built with Blazor Server
- Local document processing and embedding generation
- Vector similarity search for relevant content retrieval
- Integration with LM Studio for local LLM inference
- Automatic document monitoring and re-indexing

How to use:
1. Place your documents in the Documents folder
2. Start the application - documents will be automatically processed
3. Ask questions about your documents in the chat interface
4. The AI will provide answers based on the content of your documents

Technical Details:
- Built with .NET 8 and Blazor Server
- Uses ONNX Runtime for local embedding generation
- Implements cosine similarity for vector search
- Integrates with LM Studio's OpenAI-compatible API
- Supports text and PDF document formats

This sample document contains information about various topics to demonstrate the retrieval capabilities. You can ask questions about RAG, Blazor, document processing, or any other topic mentioned in this document.
